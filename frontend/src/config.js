// 获取后端URL的函数
const getBackendUrl = () => {
  console.log('正在获取后端URL...');

  // 优先使用构建时环境变量
  const envApiUrl = process.env.REACT_APP_API_URL;
  if (envApiUrl) {
    console.log('使用环境变量REACT_APP_API_URL:', envApiUrl);
    return envApiUrl;
  }

  // 其次使用 localStorage 中的配置 (保留灵活性)
  const storedUrl = localStorage.getItem('BACKEND_URL');
  if (storedUrl && storedUrl.startsWith('http')) {
    console.log('使用存储的后端URL:', storedUrl);
    return storedUrl;
  }

  // 尝试从当前页面URL推断后端URL
  try {
    const currentUrl = window.location.origin;
    // 如果当前页面不是在 localhost 上，尝试使用相同的域名但不同的端口
    if (!currentUrl.includes('localhost') && !currentUrl.includes('127.0.0.1')) {
      const inferredUrl = currentUrl.replace(/:\d+$/, '') + ':5006';
      console.log('从当前页面URL推断后端URL:', inferredUrl);
      return inferredUrl;
    }
  } catch (e) {
    console.warn('无法从当前页面URL推断后端URL:', e);
  }

  // 本地开发环境默认值 (作为最后的备选)
  const defaultUrl = 'http://localhost:5006';
  console.log('使用默认后端URL (备选):', defaultUrl);
  return defaultUrl;
};

// 创建基础配置对象
const config = {
  backendUrl: '',
  apiBaseUrl: '',
  appName: '薪资管理系统',
  version: 'MCHRMS-1.3.0',

  refresh() {
    try {
      const backendUrl = getBackendUrl();

      // 验证 backendUrl 是否有效
      if (!backendUrl || typeof backendUrl !== 'string' || !backendUrl.startsWith('http')) {
        console.error('无效的后端 URL:', backendUrl);
        // 使用默认值
        this.backendUrl = 'http://localhost:5006';
      } else {
        this.backendUrl = backendUrl;
      }

      // 检查 backendUrl 是否已经包含 /api 前缀
      if (this.backendUrl.endsWith('/api')) {
        this.apiBaseUrl = this.backendUrl;
      } else {
        // 添加 /api 前缀
        this.apiBaseUrl = `${this.backendUrl}/api`;
      }

      // 保存到 localStorage 以便下次使用
      if (this.backendUrl && this.backendUrl.startsWith('http')) {
        localStorage.setItem('BACKEND_URL', this.backendUrl);
      }

      console.log('配置已刷新:', {
        backendUrl: this.backendUrl,
        apiBaseUrl: this.apiBaseUrl
      });
      console.log('最终 API 基础 URL:', this.apiBaseUrl);
    } catch (error) {
      console.error('刷新配置时出错:', error);
      // 确保即使出错也有默认值
      this.backendUrl = 'http://localhost:5006';
      this.apiBaseUrl = 'http://localhost:5006/api';
    }

    // 注释掉强制本地开发的代码，让环境配置正常工作
    // this.apiBaseUrl = 'http://localhost:5006/api';
    // console.log('强制设置 apiBaseUrl 为本地后端:', this.apiBaseUrl);

    return this;
  }
};

// 初始化配置
config.refresh();

export default config;