const express = require('express');
const router = express.Router();

// 健康检查接口 (在/api路径下)
router.get('/health', (_req, res) => {
    res.status(200).json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        message: '服务器运行正常',
        version: '1.3.0'
    });
});

// 额外导出一个可用于根路径的健康检查中间件
const rootHealthCheck = (_req, res) => {
    res.status(200).json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        message: '服务器运行正常',
        version: '1.3.0'
    });
};

module.exports = router;
module.exports.rootHealthCheck = rootHealthCheck;