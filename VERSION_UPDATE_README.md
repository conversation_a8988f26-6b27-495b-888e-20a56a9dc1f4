# MCHRMS 版本号更新指南

## 概述

`update-version.sh` 是一个自动化脚本，用于统一更新 MCHRMS 项目中所有相关文件的版本号。

## 使用方法

### 基本用法

```bash
./update-version.sh <新版本号>
```

### 示例

```bash
# 更新到版本 1.4.0
./update-version.sh 1.4.0

# 更新到版本 2.0.0
./update-version.sh 2.0.0
```

## 更新的文件列表

脚本会自动更新以下文件中的版本号：

### 1. 项目配置文件
- `package.json` - 主项目版本
- `frontend/package.json` - 前端项目版本
- `backend/package.json` - 后端项目版本

### 2. 前端文件
- `frontend/src/config.js` - 前端配置中的版本号
- `frontend/src/components/Login.js` - 登录页面显示的版本号
- `frontend/src/components/AdminLogin.js` - 管理员登录页面显示的版本号
- `frontend/public/manifest.json` - PWA应用名称中的版本号

### 3. 后端文件
- `backend/routes/healthRoutes.js` - 健康检查API返回的版本号

### 4. 构建和部署文件
- `frontend/build/manifest.json` - 构建后的manifest文件（如果存在）
- `deploy/backend/package.json` - 部署目录中的后端版本（如果存在）
- `deploy/backend/routes/healthRoutes.js` - 部署目录中的健康检查路由（如果存在）
- `deploy/public/manifest.json` - 部署目录中的manifest文件（如果存在）

### 5. 工具脚本
- `backup.sh` - 备份脚本中的版本号

## 版本号格式

版本号必须遵循 **语义化版本控制** (Semantic Versioning) 格式：

```
主版本号.次版本号.修订号
```

例如：`1.4.0`, `2.0.0`, `1.3.5`

## 脚本特性

### ✅ 安全特性
- **版本号格式验证**：确保输入的版本号符合 x.y.z 格式
- **交互式确认**：执行前会显示当前版本和目标版本，需要用户确认
- **错误处理**：遇到错误会立即停止并显示错误信息

### ✅ 用户友好
- **彩色输出**：使用不同颜色区分信息类型（成功、警告、错误、信息）
- **详细日志**：显示每个文件的更新状态
- **更新摘要**：完成后显示所有更新的内容
- **后续建议**：提供建议的后续操作步骤

### ✅ 智能处理
- **自动检测**：自动检测当前版本号
- **条件更新**：只更新存在的文件（如构建文件、部署文件）
- **全面覆盖**：确保所有相关文件都得到更新

## 完整的版本更新流程

推荐的完整版本更新流程：

```bash
# 1. 更新版本号
./update-version.sh 1.4.0

# 2. 重新构建前端
cd frontend && npm run build && cd ..

# 3. 测试应用功能
npm start

# 4. 创建备份
./backup.sh

# 5. 部署到NAS（如果需要）
./deploy-to-nas.sh

# 6. 提交代码变更
git add .
git commit -m "chore: bump version to 1.4.0"
git tag v1.4.0
git push origin main --tags
```

## 注意事项

1. **执行前备份**：建议在执行版本更新前先创建项目备份
2. **测试验证**：更新后应该测试应用的核心功能
3. **构建更新**：记得重新构建前端以确保构建文件也包含新版本号
4. **Git提交**：建议将版本更新作为单独的提交，并创建对应的Git标签

## 故障排除

### 权限问题
如果遇到权限错误，确保脚本有执行权限：
```bash
chmod +x update-version.sh
```

### 文件不存在
脚本会智能跳过不存在的文件，这是正常行为。

### 版本号格式错误
确保版本号格式为 `x.y.z`，例如 `1.4.0` 而不是 `v1.4.0` 或 `1.4`。

## 支持的操作系统

- ✅ macOS
- ✅ Linux
- ❓ Windows (需要 Git Bash 或 WSL)

---

**提示**：这个脚本是为了简化版本管理流程而创建的。如果你发现有遗漏的文件需要更新版本号，请修改脚本或联系开发团队。
