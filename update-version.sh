#!/bin/bash

# MCHRMS 项目版本号统一更新脚本
# 使用方式: ./update-version.sh <新版本号>
# 例如: ./update-version.sh 1.4.0

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 错误处理函数
handle_error() {
    echo -e "${RED}错误: $1${NC}"
    exit 1
}

# 成功信息函数
success_msg() {
    echo -e "${GREEN}✅ $1${NC}"
}

# 警告信息函数
warning_msg() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# 信息函数
info_msg() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# 检查参数
if [ $# -ne 1 ]; then
    echo -e "${RED}错误: 请提供新的版本号${NC}"
    echo "使用方式: $0 <新版本号>"
    echo "例如: $0 1.4.0"
    exit 1
fi

NEW_VERSION="$1"

# 验证版本号格式 (x.y.z)
if ! [[ $NEW_VERSION =~ ^[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
    handle_error "版本号格式不正确，请使用 x.y.z 格式 (例如: 1.4.0)"
fi

echo -e "${BLUE}🚀 开始更新 MCHRMS 项目版本号到 ${NEW_VERSION}...${NC}"

# 获取当前版本号
CURRENT_VERSION=$(grep '"version":' package.json | head -1 | sed 's/.*"version": *"\([^"]*\)".*/\1/')
info_msg "当前版本: ${CURRENT_VERSION}"
info_msg "目标版本: ${NEW_VERSION}"

# 确认更新
echo -n "确认要将版本号从 ${CURRENT_VERSION} 更新到 ${NEW_VERSION} 吗? (y/N): "
read -r confirm
if [[ ! $confirm =~ ^[Yy]$ ]]; then
    echo "取消更新"
    exit 0
fi

echo ""
echo -e "${BLUE}📝 开始更新各文件中的版本号...${NC}"

# 1. 更新主项目 package.json
info_msg "更新主项目 package.json"
sed -i '' "s/\"version\": \"[0-9]*\.[0-9]*\.[0-9]*\"/\"version\": \"${NEW_VERSION}\"/" package.json
success_msg "主项目 package.json 已更新"

# 2. 更新前端 package.json
info_msg "更新前端 package.json"
sed -i '' "s/\"version\": \"[0-9]*\.[0-9]*\.[0-9]*\"/\"version\": \"${NEW_VERSION}\"/" frontend/package.json
success_msg "前端 package.json 已更新"

# 3. 更新后端 package.json
info_msg "更新后端 package.json"
sed -i '' "s/\"version\": \"[0-9]*\.[0-9]*\.[0-9]*\"/\"version\": \"${NEW_VERSION}\"/" backend/package.json
success_msg "后端 package.json 已更新"

# 4. 更新前端配置文件
info_msg "更新前端配置文件 (config.js)"
sed -i '' "s/version: 'MCHRMS-[0-9]*\.[0-9]*\.[0-9]*'/version: 'MCHRMS-${NEW_VERSION}'/" frontend/src/config.js
success_msg "前端配置文件已更新"

# 5. 更新登录页面版本号
info_msg "更新登录页面版本号"
sed -i '' "s/版本号: MCHRMS-[0-9]*\.[0-9]*\.[0-9]*/版本号: MCHRMS-${NEW_VERSION}/" frontend/src/components/Login.js
success_msg "登录页面版本号已更新"

# 6. 更新管理员登录页面版本号
info_msg "更新管理员登录页面版本号"
sed -i '' "s/管理员入口 MCHRMS-[0-9]*\.[0-9]*\.[0-9]*/管理员入口 MCHRMS-${NEW_VERSION}/" frontend/src/components/AdminLogin.js
success_msg "管理员登录页面版本号已更新"

# 7. 更新前端 manifest.json
info_msg "更新前端 manifest.json"
sed -i '' "s/\"name\": \"MCHRMS v[0-9]*\.[0-9]*\.[0-9]*\"/\"name\": \"MCHRMS v${NEW_VERSION}\"/" frontend/public/manifest.json
success_msg "前端 manifest.json 已更新"

# 8. 更新后端健康检查路由
info_msg "更新后端健康检查路由"
sed -i '' "s/version: '[0-9]*\.[0-9]*\.[0-9]*'/version: '${NEW_VERSION}'/g" backend/routes/healthRoutes.js
success_msg "后端健康检查路由已更新"

# 9. 更新备份脚本版本号
info_msg "更新备份脚本版本号"
sed -i '' "s/VERSION=\"[0-9]*\.[0-9]*\.[0-9]*\"/VERSION=\"${NEW_VERSION}\"/" backup.sh
success_msg "备份脚本版本号已更新"

# 10. 如果存在构建文件，也更新它们
if [ -f "frontend/build/manifest.json" ]; then
    info_msg "更新前端构建文件 manifest.json"
    sed -i '' "s/\"name\": \"MCHRMS v[0-9]*\.[0-9]*\.[0-9]*\"/\"name\": \"MCHRMS v${NEW_VERSION}\"/" frontend/build/manifest.json
    success_msg "前端构建文件已更新"
fi

# 11. 如果存在部署文件，也更新它们
if [ -d "deploy" ]; then
    info_msg "更新部署文件"
    if [ -f "deploy/backend/package.json" ]; then
        sed -i '' "s/\"version\": \"[0-9]*\.[0-9]*\.[0-9]*\"/\"version\": \"${NEW_VERSION}\"/" deploy/backend/package.json
    fi
    if [ -f "deploy/backend/routes/healthRoutes.js" ]; then
        sed -i '' "s/version: '[0-9]*\.[0-9]*\.[0-9]*'/version: '${NEW_VERSION}'/g" deploy/backend/routes/healthRoutes.js
    fi
    if [ -f "deploy/public/manifest.json" ]; then
        sed -i '' "s/\"name\": \"MCHRMS v[0-9]*\.[0-9]*\.[0-9]*\"/\"name\": \"MCHRMS v${NEW_VERSION}\"/" deploy/public/manifest.json
    fi
    success_msg "部署文件已更新"
fi

echo ""
echo -e "${GREEN}🎉 版本号更新完成！${NC}"
echo ""
echo -e "${BLUE}📋 更新摘要:${NC}"
echo "  • 主项目版本: ${CURRENT_VERSION} → ${NEW_VERSION}"
echo "  • 前端版本: ${CURRENT_VERSION} → ${NEW_VERSION}"
echo "  • 后端版本: ${CURRENT_VERSION} → ${NEW_VERSION}"
echo "  • 登录页面版本: MCHRMS-${NEW_VERSION}"
echo "  • 管理员页面版本: MCHRMS-${NEW_VERSION}"
echo "  • 健康检查API版本: ${NEW_VERSION}"
echo "  • 备份脚本版本: ${NEW_VERSION}"
echo ""
echo -e "${YELLOW}📝 建议的后续步骤:${NC}"
echo "  1. 重新构建前端: cd frontend && npm run build"
echo "  2. 测试应用功能"
echo "  3. 创建项目备份: ./backup.sh"
echo "  4. 部署到NAS: ./deploy-to-nas.sh"
echo "  5. 提交代码变更到Git"
echo ""
echo -e "${GREEN}✅ 版本更新脚本执行完成！${NC}"
